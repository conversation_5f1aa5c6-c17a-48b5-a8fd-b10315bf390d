#!/usr/bin/env node

/**
 * Example: Using Shopify MCP API with OpenRouter
 * 
 * This demonstrates how to use the HTTP API wrapper with OpenRouter
 */

import fetch from 'node-fetch';

class OpenRouterShopifyClient {
  constructor(openRouterApiKey, mcpApiUrl = 'http://localhost:3000') {
    this.openRouterApiKey = openRouterApiKey;
    this.mcpApiUrl = mcpApiUrl;
  }

  async searchShopifyDocs(query) {
    const response = await fetch(`${this.mcpApiUrl}/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query })
    });
    
    if (!response.ok) {
      throw new Error(`MCP API error: ${response.statusText}`);
    }
    
    return await response.json();
  }

  async getShopifySchema(query) {
    const response = await fetch(`${this.mcpApiUrl}/schema`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query })
    });
    
    if (!response.ok) {
      throw new Error(`MCP API error: ${response.statusText}`);
    }
    
    return await response.json();
  }

  async queryOpenRouter(messages, model = 'anthropic/claude-3.5-sonnet') {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openRouterApiKey}`,
        'Content-Type': 'application/json',
        'X-Title': 'Shopify MCP Integration'
      },
      body: JSON.stringify({
        model,
        messages,
        max_tokens: 4000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async askShopifyQuestion(question) {
    console.log(`\n🤔 Question: ${question}`);
    
    let context = '';
    
    // Determine what Shopify information we need
    if (question.toLowerCase().includes('product')) {
      console.log('📚 Searching for product information...');
      const searchResult = await this.searchShopifyDocs('product creation API');
      context += `\nShopify Documentation:\n${JSON.stringify(searchResult.result, null, 2)}`;
      
      console.log('🔍 Getting product schema...');
      const schemaResult = await this.getShopifySchema('Product');
      context += `\nGraphQL Schema:\n${JSON.stringify(schemaResult.result, null, 2)}`;
    }
    
    if (question.toLowerCase().includes('order')) {
      console.log('📚 Searching for order information...');
      const searchResult = await this.searchShopifyDocs('order management API');
      context += `\nShopify Documentation:\n${JSON.stringify(searchResult.result, null, 2)}`;
    }

    // Build messages for OpenRouter
    const messages = [
      {
        role: 'system',
        content: `You are a Shopify development expert. Use the provided Shopify documentation and schema information to give accurate, helpful answers about Shopify development.

Context from Shopify:${context}`
      },
      {
        role: 'user',
        content: question
      }
    ];

    console.log('🤖 Getting response from OpenRouter...');
    const response = await this.queryOpenRouter(messages);
    
    console.log(`\n✅ Answer: ${response}`);
    return response;
  }
}

// Example usage
async function main() {
  const openRouterApiKey = process.env.OPENROUTER_API_KEY;
  
  if (!openRouterApiKey) {
    console.error('❌ Please set OPENROUTER_API_KEY environment variable');
    console.log('Get your API key from: https://openrouter.ai/keys');
    process.exit(1);
  }

  const client = new OpenRouterShopifyClient(openRouterApiKey);
  
  // Wait for MCP API to be ready
  console.log('⏳ Waiting for Shopify MCP API to be ready...');
  let ready = false;
  while (!ready) {
    try {
      const response = await fetch('http://localhost:3000/health');
      if (response.ok) {
        ready = true;
        console.log('✅ Shopify MCP API is ready!');
      }
    } catch (error) {
      console.log('⏳ MCP API not ready yet, waiting...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  // Example questions
  const questions = [
    "How do I create a product using Shopify's GraphQL API?",
    "What fields are available on the Product type?",
    "How do I handle product variants in Shopify?",
    "What's the best way to manage inventory in Shopify?"
  ];

  try {
    for (const question of questions) {
      await client.askShopifyQuestion(question);
      console.log('\n' + '='.repeat(80));
      
      // Small delay between questions
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { OpenRouterShopifyClient };
