# This file stores configurations for your Shopify app.

scopes = "write_products"

[webhooks]
api_version = "2024-10"

  # Handled by: /app/routes/webhooks.app.uninstalled.tsx
  [[webhooks.subscriptions]]
  uri = "/webhooks/app/uninstalled"
  topics = ["app/uninstalled"]

  # Handled by: /app/routes/webhooks.app.scopes_update.tsx
  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

  # Webhooks can have filters
  # Only receive webhooks for product updates with a product price >= 10.00
  # See: https://shopify.dev/docs/apps/build/webhooks/customize/filters
  # [[webhooks.subscriptions]]
  # topics = ["products/update"]
  # uri = "/webhooks/products/update"
  # filter = "variants.price:>=10.00"

  # Mandatory compliance topic for public apps only
  # See: https://shopify.dev/docs/apps/build/privacy-law-compliance
  # [[webhooks.subscriptions]]
  # uri = "/webhooks/customers/data_request"
  # compliance_topics = ["customers/data_request"]

  # [[webhooks.subscriptions]]
  # uri = "/webhooks/customers/redact"
  # compliance_topics = ["customers/redact"]

  # [[webhooks.subscriptions]]
  # uri = "/webhooks/shop/redact"
  # compliance_topics = ["shop/redact"]
