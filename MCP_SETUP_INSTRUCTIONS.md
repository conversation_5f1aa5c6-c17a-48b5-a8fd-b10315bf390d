# Shopify MCP Server Setup for Claude for Code (VS Code)

## ✅ Setup Complete!

Your Shopify MCP server has been successfully configured for use with <PERSON> for Code in VS Code.

## What's Been Done

1. **Built the project**: The TypeScript source has been compiled to `dist/index.js`
2. **Configured VS Code settings**: Added MCP server configuration to `.vscode/settings.json`
3. **Created alternative configurations**: Additional config files for different use cases

## Current Configuration

The MCP server is now configured in your VS Code workspace settings (`.vscode/settings.json`) with:

```json
{
  "claude.mcpServers": {
    "shopify-dev-mcp": {
      "command": "node",
      "args": ["${workspaceFolder}/dist/index.js"],
      "env": {
        "OPT_OUT_INSTRUMENTATION": "true"
      }
    }
  }
}
```

## How to Use

1. **Restart VS Code** to ensure the new MCP server configuration is loaded
2. **Open Claude for Code** in VS Code
3. The Shopify MCP server should now be available with these tools:
   - `search_dev_docs` - Search shopify.dev documentation
   - `introspect_admin_schema` - Access and search Shopify Admin GraphQL schema
   - `fetch_docs_by_path` - Retrieve documents from shopify.dev
   - `get_started` - Get started with Shopify APIs (Admin, Functions, etc.)

## Available Prompts

- `shopify_admin_graphql` - Help you write GraphQL operations for the Shopify Admin API

## Alternative Configurations

### With Polaris Support (Experimental)
If you want to enable Polaris Web Components documentation, replace your `.vscode/settings.json` content with the content from `.vscode/settings_with_polaris.json`.

### For Claude Desktop (if needed later)
- `claude_desktop_config.json` - Basic configuration for Claude Desktop
- `claude_desktop_config_with_polaris.json` - With Polaris support enabled

## Troubleshooting

1. **Server not starting**: Make sure Node.js is installed and accessible
2. **Changes not reflected**: Restart VS Code after modifying the configuration
3. **Permission issues**: Ensure the `dist/index.js` file is executable

## Testing the Server

To test if the server works correctly, you can run:
```bash
node dist/index.js
```

The server should start and display a message like "Shopify Dev MCP Server v1.1.0 running on stdio".

## Environment Variables

- `OPT_OUT_INSTRUMENTATION=true` - Disables telemetry collection
- `POLARIS_UNIFIED=true` - Enables Polaris Web Components documentation (experimental)

## Next Steps

1. Restart VS Code
2. Open Claude for Code
3. Try using one of the Shopify tools, for example:
   - Ask Claude to "search for product creation in Shopify docs"
   - Ask Claude to "help me write a GraphQL query to fetch products"

The MCP server will provide Claude with access to Shopify documentation and schema information to help you with Shopify development tasks.
