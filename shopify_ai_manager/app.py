#!/usr/bin/env python3
"""
Shopify AI Manager - Flask Application
A Python Flask system to control Shopify storefronts using AI
"""

import os
import json
import subprocess
import requests
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from flask_session import Session
import uuid
from datetime import datetime
import threading
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['SESSION_TYPE'] = 'filesystem'
Session(app)

# Configuration
OPENROUTER_API_KEY = os.environ.get('OPENROUTER_API_KEY')
MCP_SERVER_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dist', 'index.js')

class ShopifyMCPClient:
    """Client to interact with Shopify MCP Server"""
    
    def __init__(self):
        self.mcp_process = None
        self.is_connected = False
        
    def start_mcp_server(self):
        """Start the Shopify MCP server process"""
        try:
            print("Starting Shopify MCP Server...")
            self.mcp_process = subprocess.Popen(
                ['node', MCP_SERVER_PATH],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env={**os.environ, 'OPT_OUT_INSTRUMENTATION': 'true'},
                text=True
            )
            
            # Give the server time to start
            time.sleep(2)
            
            if self.mcp_process.poll() is None:
                self.is_connected = True
                print("✅ Shopify MCP Server started successfully")
                return True
            else:
                print("❌ Failed to start MCP Server")
                return False
                
        except Exception as e:
            print(f"❌ Error starting MCP Server: {e}")
            return False
    
    def stop_mcp_server(self):
        """Stop the MCP server process"""
        if self.mcp_process:
            self.mcp_process.terminate()
            self.mcp_process = None
            self.is_connected = False
            print("🛑 MCP Server stopped")
    
    def search_shopify_docs(self, query):
        """Search Shopify documentation using MCP server"""
        # For now, return mock data - we'll implement actual MCP communication later
        return {
            "query": query,
            "results": [
                {
                    "title": f"Shopify Documentation for: {query}",
                    "content": f"This is documentation content related to {query}. The MCP server would provide real Shopify documentation here.",
                    "url": "https://shopify.dev/docs"
                }
            ]
        }
    
    def get_admin_schema(self, query):
        """Get Shopify Admin GraphQL schema information"""
        # Mock data for now
        return {
            "query": query,
            "schema": f"GraphQL schema information for {query} would be provided by the MCP server here."
        }

# Global MCP client instance
mcp_client = ShopifyMCPClient()

class AIAssistant:
    """AI Assistant using OpenRouter for intelligent responses"""
    
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
    
    def get_ai_response(self, prompt, context="", module="general"):
        """Get AI response with Shopify context"""
        if not self.api_key:
            return "OpenRouter API key not configured. Please set OPENROUTER_API_KEY environment variable."
        
        system_prompts = {
            "seo": "You are a Shopify SEO expert. Help optimize store SEO, meta tags, product descriptions, and search rankings.",
            "ads": "You are a Shopify advertising expert. Help create and optimize ad campaigns, product listings, and marketing strategies.",
            "email": "You are a Shopify email marketing expert. Help create email campaigns, automation workflows, and customer engagement strategies.",
            "support": "You are a Shopify customer support expert. Help with customer service, chatbot responses, and support automation.",
            "general": "You are a Shopify development and management expert. Provide helpful guidance for store management."
        }
        
        messages = [
            {
                "role": "system",
                "content": f"{system_prompts.get(module, system_prompts['general'])}\n\nShopify Context:\n{context}"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        try:
            response = requests.post(
                self.base_url,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                    "X-Title": "Shopify AI Manager"
                },
                json={
                    "model": "google/gemini-2.5-pro-preview",
                    "messages": messages,
                    "max_tokens": 4000
                }
            )
            
            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"]
            else:
                return f"Error: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"Error communicating with AI: {str(e)}"

# Global AI assistant instance
ai_assistant = AIAssistant(OPENROUTER_API_KEY)

@app.route('/')
def index():
    """Main dashboard"""
    return render_template('index.html')

@app.route('/connect')
def connect():
    """Shopify connection page"""
    return render_template('connect.html')

@app.route('/api/connect-store', methods=['POST'])
def connect_store():
    """Connect to a Shopify store"""
    data = request.get_json()
    
    store_url = data.get('store_url', '').strip()
    access_token = data.get('access_token', '').strip()
    
    if not store_url or not access_token:
        return jsonify({"error": "Store URL and Access Token are required"}), 400
    
    # Validate store URL format
    if not store_url.endswith('.myshopify.com'):
        if not store_url.startswith('http'):
            store_url = f"https://{store_url}.myshopify.com"
    
    # Store connection info in session (in production, use a database)
    session['shopify_store'] = {
        'url': store_url,
        'access_token': access_token,
        'connected_at': datetime.now().isoformat(),
        'connection_id': str(uuid.uuid4())
    }
    
    return jsonify({
        "success": True,
        "message": "Successfully connected to Shopify store",
        "store_url": store_url
    })

@app.route('/api/disconnect-store', methods=['POST'])
def disconnect_store():
    """Disconnect from Shopify store"""
    session.pop('shopify_store', None)
    return jsonify({"success": True, "message": "Disconnected from Shopify store"})

@app.route('/api/store-status')
def store_status():
    """Get current store connection status"""
    store_info = session.get('shopify_store')
    if store_info:
        return jsonify({
            "connected": True,
            "store_url": store_info['url'],
            "connected_at": store_info['connected_at']
        })
    else:
        return jsonify({"connected": False})

@app.route('/seo')
def seo_module():
    """SEO optimization module"""
    return render_template('modules/seo.html')

@app.route('/ads')
def ads_module():
    """Advertising management module"""
    return render_template('modules/ads.html')

@app.route('/email')
def email_module():
    """Email marketing module"""
    return render_template('modules/email.html')

@app.route('/support')
def support_module():
    """Customer support/chatbot module"""
    return render_template('modules/support.html')

@app.route('/api/ai-chat', methods=['POST'])
def ai_chat():
    """AI chat endpoint for all modules"""
    data = request.get_json()
    
    message = data.get('message', '').strip()
    module = data.get('module', 'general')
    
    if not message:
        return jsonify({"error": "Message is required"}), 400
    
    # Get Shopify context if store is connected
    context = ""
    store_info = session.get('shopify_store')
    if store_info:
        # Get relevant Shopify documentation/schema based on the query
        if any(keyword in message.lower() for keyword in ['product', 'inventory', 'order']):
            shopify_docs = mcp_client.search_shopify_docs(message)
            context += f"Shopify Documentation: {json.dumps(shopify_docs, indent=2)}\n"
        
        context += f"Connected Store: {store_info['url']}\n"
    
    # Get AI response
    ai_response = ai_assistant.get_ai_response(message, context, module)
    
    return jsonify({
        "response": ai_response,
        "module": module,
        "timestamp": datetime.now().isoformat()
    })

@app.route('/api/mcp-status')
def mcp_status():
    """Get MCP server status"""
    return jsonify({
        "connected": mcp_client.is_connected,
        "server_path": MCP_SERVER_PATH
    })

@app.route('/api/start-mcp', methods=['POST'])
def start_mcp():
    """Start MCP server"""
    success = mcp_client.start_mcp_server()
    return jsonify({
        "success": success,
        "connected": mcp_client.is_connected
    })

@app.route('/api/stop-mcp', methods=['POST'])
def stop_mcp():
    """Stop MCP server"""
    mcp_client.stop_mcp_server()
    return jsonify({
        "success": True,
        "connected": mcp_client.is_connected
    })

if __name__ == '__main__':
    # Start MCP server on application startup
    print("🚀 Starting Shopify AI Manager...")
    
    # Start MCP server in a separate thread
    def start_mcp_async():
        time.sleep(1)  # Give Flask time to start
        mcp_client.start_mcp_server()
    
    mcp_thread = threading.Thread(target=start_mcp_async, daemon=True)
    mcp_thread.start()
    
    # Register cleanup function
    import atexit
    atexit.register(mcp_client.stop_mcp_server)
    
    print("🌐 Starting Flask application...")
    app.run(debug=True, host='0.0.0.0', port=5000)
